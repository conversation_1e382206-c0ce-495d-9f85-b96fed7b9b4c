{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/carousel/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Carousel from './src/carousel.vue'\nimport CarouselItem from './src/carousel-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCarousel: SFCWithInstall<typeof Carousel> & {\n  CarouselItem: typeof CarouselItem\n} = withInstall(Carousel, {\n  CarouselItem,\n})\n\nexport default ElCarousel\n\nexport const ElCarouselItem: SFCWithInstall<typeof CarouselItem> =\n  withNoopInstall(CarouselItem)\n\nexport * from './src/carousel'\nexport * from './src/carousel-item'\nexport * from './src/constants'\n\nexport type { CarouselInstance, CarouselItemInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}