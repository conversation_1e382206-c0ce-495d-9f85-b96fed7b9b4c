{"version": 3, "file": "avatar2.mjs", "sources": ["../../../../../../packages/components/avatar/src/avatar.vue"], "sourcesContent": ["<template>\n  <span :class=\"avatarClass\" :style=\"sizeStyle\">\n    <img\n      v-if=\"(src || srcSet) && !hasLoadError\"\n      :src=\"src\"\n      :alt=\"alt\"\n      :srcset=\"srcSet\"\n      :style=\"fitStyle\"\n      @error=\"handleError\"\n    />\n    <el-icon v-else-if=\"icon\">\n      <component :is=\"icon\" />\n    </el-icon>\n    <slot v-else />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { addUnit, isNumber, isString } from '@element-plus/utils'\nimport { avatarEmits, avatarProps } from './avatar'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElAvatar',\n})\n\nconst props = defineProps(avatarProps)\nconst emit = defineEmits(avatarEmits)\n\nconst ns = useNamespace('avatar')\n\nconst hasLoadError = ref(false)\n\nconst avatarClass = computed(() => {\n  const { size, icon, shape } = props\n  const classList = [ns.b()]\n  if (isString(size)) classList.push(ns.m(size))\n  if (icon) classList.push(ns.m('icon'))\n  if (shape) classList.push(ns.m(shape))\n  return classList\n})\n\nconst sizeStyle = computed(() => {\n  const { size } = props\n  return isNumber(size)\n    ? (ns.cssVarBlock({\n        size: addUnit(size) || '',\n      }) as CSSProperties)\n    : undefined\n})\n\nconst fitStyle = computed<CSSProperties>(() => ({\n  objectFit: props.fit,\n}))\n\n// need reset hasLoadError to false if src changed\nwatch(\n  () => props.src,\n  () => (hasLoadError.value = false)\n)\n\nfunction handleError(e: Event) {\n  hasLoadError.value = true\n  emit('error', e)\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCA0Bc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAEhC,IAAM,MAAA,YAAA,GAAe,IAAI,KAAK,CAAA,CAAA;AAE9B,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAA,MAAM,EAAE,IAAA,EAAM,IAAM,EAAA,KAAA,EAAU,GAAA,KAAA,CAAA;AAC9B,MAAA,MAAM,SAAY,GAAA,CAAC,EAAG,CAAA,CAAA,EAAG,CAAA,CAAA;AACzB,MAAI,IAAA,QAAA,CAAS,IAAI,CAAG;AACpB,QAAA,SAAoB,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAQ,CAAA,CAAA,CAAA;AAC5B,MAAA,IAAI;AACJ,QAAO,SAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,MACR,IAAA,KAAA;AAED,QAAM,SAAA,CAAA,IAAY,WAAe,CAAA,CAAA,CAAA;AAC/B,MAAM,OAAA,SAAW,CAAA;AACjB,KAAA,CAAA,CAAA;AACoB,IACd,MAAA,SAAc,GAAA,QAAS,CAAA,MAAA;AAAA,MACzB,MACA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MACL,OAAA,QAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA;AAED,QAAM,IAAA,EAAA,OAAW,UAA+B,EAAA;AAAA,iBACnC,CAAM;AAAA,KACjB,CAAA,CAAA;AAGF,IAAA,MAAA,QAAA,GAAA,QAAA,CAAA,OAAA;AAAA,MACE,SAAY,EAAA,KAAA,CAAA,GAAA;AAAA,KACZ,CAAA,CAAA,CAAA;AAA4B,IAC9B,KAAA,CAAA,MAAA,KAAA,CAAA,GAAA,EAAA,MAAA,YAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAEA,IAAA,SAAS,YAAY,CAAU,EAAA;AAC7B,MAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AACrB,MAAA,IAAA,CAAK,SAAS,CAAC,CAAA,CAAA;AAAA,KACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;"}