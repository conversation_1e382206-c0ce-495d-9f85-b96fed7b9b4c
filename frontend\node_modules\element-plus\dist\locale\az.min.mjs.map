{"version": 3, "file": "az.min.mjs", "sources": ["../../../../packages/locale/lang/az.ts"], "sourcesContent": ["export default {\n  name: 'az',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Təsdiqlə',\n      clear: 'Təmizlə',\n    },\n    datepicker: {\n      now: '<PERSON>ndi',\n      today: '<PERSON><PERSON>ü<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON>',\n      clear: 'Təmizlə',\n      confirm: 'Təsdiqlə',\n      selectDate: 'Tarix seç',\n      selectTime: 'Saat seç',\n      startDate: '<PERSON>şlanğıc Tarixi',\n      startTime: '<PERSON>şlanğıc Saatı',\n      endDate: 'Bitm<PERSON> Tarixi',\n      endTime: 'Bitmə Saatı',\n      prevYear: 'Öncəki il',\n      nextYear: 'Sonrakı il',\n      prevMonth: 'Öncəki ay',\n      nextMonth: 'Sonrakı ay',\n      year: '',\n      month1: 'Yanvar',\n      month2: 'Fevral',\n      month3: 'Mart',\n      month4: 'Aprel',\n      month5: 'May',\n      month6: 'İyun',\n      month7: 'İyul',\n      month8: 'Avqust',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Oktyabr',\n      month11: 'Noyabr',\n      month12: 'Dekabr',\n      week: 'həftə',\n      weeks: {\n        sun: 'Baz',\n        mon: 'B.e',\n        tue: 'Ç.a',\n        wed: 'Çər',\n        thu: 'C.a',\n        fri: 'Cüm',\n        sat: 'Şən',\n      },\n      months: {\n        jan: 'Yan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'İyn',\n        jul: 'İyl',\n        aug: 'Avq',\n        sep: 'Sen',\n        oct: 'Okt',\n        nov: 'Noy',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Yüklənir',\n      noMatch: 'Nəticə tapılmadı',\n      noData: 'Məlumat yoxdur',\n      placeholder: 'Seç',\n    },\n    mention: {\n      loading: 'Yüklənir',\n    },\n    cascader: {\n      noMatch: 'Nəticə tapılmadı',\n      loading: 'Yüklənir',\n      placeholder: 'Seç',\n      noData: 'Məlumat yoxdur',\n    },\n    pagination: {\n      goto: 'Get',\n      pagesize: '/səhifə',\n      total: 'Toplam {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'Təsdiqlə',\n      cancel: 'İmtina',\n      error: 'Səhv',\n    },\n    upload: {\n      deleteTip: 'Sürüşdürmədən sonra sil',\n      delete: 'Sil',\n      preview: 'Ön izlə',\n      continue: 'Davam et',\n    },\n    table: {\n      emptyText: 'Məlumat yoxdur',\n      confirmFilter: 'Təsdiqlə',\n      resetFilter: 'Sıfırla',\n      clearFilter: 'Bütün',\n      sumText: 'Cəmi',\n    },\n    tree: {\n      emptyText: 'Məlumat yoxdur',\n    },\n    transfer: {\n      noMatch: 'Nəticə tapılmadı',\n      noData: 'Məlumat yoxdur',\n      titles: ['Siyahı 1', 'Siyahı 2'],\n      filterPlaceholder: 'Kəlimələri daxil et',\n      noCheckedFormat: '{total} ədəd',\n      hasCheckedFormat: '{checked}/{total} seçildi',\n    },\n    image: {\n      error: 'SƏHV', // to be translated\n    },\n    pageHeader: {\n      title: 'Geri', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Bəli', // to be translated\n      cancelButtonText: 'Xeyr', // to be translated\n    },\n    empty: {\n      description: 'Məlumat yoxdur',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,iCAAiC,CAAC,SAAS,CAAC,qCAAqC,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,sCAAsC,CAAC,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,aAAa,CAAC,oBAAoB,CAAC,WAAW,CAAC,mBAAmB,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,iBAAiB,CAAC,oCAAoC,CAAC,eAAe,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}