<template>
  <div class="sidebar-container">
    <!-- Logo -->
    <div class="sidebar-logo-container" :class="{ collapse: !sidebar.opened }">
      <transition name="sidebarLogoFade">
        <router-link v-if="sidebar.opened" key="collapse" class="sidebar-logo-link" to="/">
          <img v-if="logo" :src="logo" class="sidebar-logo" />
          <el-icon v-else class="sidebar-logo" size="32" color="#409eff">
            <Setting />
          </el-icon>
          <h1 class="sidebar-title">{{ title }}</h1>
        </router-link>
        <router-link v-else key="expand" class="sidebar-logo-link" to="/">
          <img v-if="logo" :src="logo" class="sidebar-logo" />
          <el-icon v-else class="sidebar-logo" size="32" color="#409eff">
            <Setting />
          </el-icon>
        </router-link>
      </transition>
    </div>
    
    <!-- 菜单 -->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="!sidebar.opened"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <SidebarItem
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Setting } from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebar = computed(() => appStore.sidebar)
const logo = computed(() => appStore.settings.logo)
const title = computed(() => appStore.settings.title)

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 路由菜单
const routes = computed(() => {
  return [
    {
      path: '/dashboard',
      name: 'Dashboard',
      meta: {
        title: '仪表盘',
        icon: 'DataAnalysis'
      }
    },
    {
      path: '/bom',
      name: 'BOM',
      meta: {
        title: 'BOM管理',
        icon: 'List'
      },
      children: [
        {
          path: '/bom/list',
          name: 'BOMList',
          meta: {
            title: 'BOM清单',
            icon: 'Document'
          }
        },
        {
          path: '/bom/structure',
          name: 'BOMStructure',
          meta: {
            title: 'BOM结构',
            icon: 'Share'
          }
        },
        {
          path: '/bom/version',
          name: 'BOMVersion',
          meta: {
            title: '版本管理',
            icon: 'Clock'
          }
        }
      ]
    },
    {
      path: '/customer',
      name: 'Customer',
      meta: {
        title: '客户服务',
        icon: 'User'
      },
      children: [
        {
          path: '/customer/list',
          name: 'CustomerList',
          meta: {
            title: '客户列表',
            icon: 'UserFilled'
          }
        },
        {
          path: '/customer/service',
          name: 'CustomerService',
          meta: {
            title: '服务记录',
            icon: 'ChatDotRound'
          }
        },
        {
          path: '/customer/feedback',
          name: 'CustomerFeedback',
          meta: {
            title: '客户反馈',
            icon: 'MessageBox'
          }
        }
      ]
    },
    {
      path: '/workorder',
      name: 'WorkOrder',
      meta: {
        title: '工单管理',
        icon: 'Tickets'
      },
      children: [
        {
          path: '/workorder/list',
          name: 'WorkOrderList',
          meta: {
            title: '工单列表',
            icon: 'Document'
          }
        },
        {
          path: '/workorder/dispatch',
          name: 'WorkOrderDispatch',
          meta: {
            title: '工单派发',
            icon: 'Position'
          }
        },
        {
          path: '/workorder/track',
          name: 'WorkOrderTrack',
          meta: {
            title: '进度跟踪',
            icon: 'TrendCharts'
          }
        }
      ]
    },
    {
      path: '/spare-parts',
      name: 'SpareParts',
      meta: {
        title: '备件管理',
        icon: 'Box'
      },
      children: [
        {
          path: '/spare-parts/inventory',
          name: 'SparePartsInventory',
          meta: {
            title: '库存管理',
            icon: 'Goods'
          }
        },
        {
          path: '/spare-parts/procurement',
          name: 'SparePartsProcurement',
          meta: {
            title: '采购管理',
            icon: 'ShoppingCart'
          }
        },
        {
          path: '/spare-parts/allocation',
          name: 'SparePartsAllocation',
          meta: {
            title: '调拨管理',
            icon: 'Switch'
          }
        }
      ]
    },
    {
      path: '/maintenance',
      name: 'Maintenance',
      meta: {
        title: '维修管理',
        icon: 'Tools'
      },
      children: [
        {
          path: '/maintenance/plan',
          name: 'MaintenancePlan',
          meta: {
            title: '维修计划',
            icon: 'Calendar'
          }
        },
        {
          path: '/maintenance/record',
          name: 'MaintenanceRecord',
          meta: {
            title: '维修记录',
            icon: 'Notebook'
          }
        },
        {
          path: '/maintenance/cost',
          name: 'MaintenanceCost',
          meta: {
            title: '成本分析',
            icon: 'Money'
          }
        }
      ]
    },
    {
      path: '/knowledge',
      name: 'Knowledge',
      meta: {
        title: '知识库',
        icon: 'Reading'
      },
      children: [
        {
          path: '/knowledge/articles',
          name: 'KnowledgeArticles',
          meta: {
            title: '知识文章',
            icon: 'Document'
          }
        },
        {
          path: '/knowledge/faq',
          name: 'KnowledgeFAQ',
          meta: {
            title: '常见问题',
            icon: 'QuestionFilled'
          }
        },
        {
          path: '/knowledge/manual',
          name: 'KnowledgeManual',
          meta: {
            title: '操作手册',
            icon: 'Notebook'
          }
        }
      ]
    },
    {
      path: '/reports',
      name: 'Reports',
      meta: {
        title: '报表分析',
        icon: 'PieChart'
      },
      children: [
        {
          path: '/reports/service',
          name: 'ServiceReports',
          meta: {
            title: '服务报表',
            icon: 'DataAnalysis'
          }
        },
        {
          path: '/reports/cost',
          name: 'CostReports',
          meta: {
            title: '成本报表',
            icon: 'TrendCharts'
          }
        },
        {
          path: '/reports/performance',
          name: 'PerformanceReports',
          meta: {
            title: '绩效报表',
            icon: 'Histogram'
          }
        }
      ]
    },
    {
      path: '/system',
      name: 'System',
      meta: {
        title: '系统设置',
        icon: 'Setting'
      },
      children: [
        {
          path: '/system/users',
          name: 'SystemUsers',
          meta: {
            title: '用户管理',
            icon: 'User'
          }
        },
        {
          path: '/system/roles',
          name: 'SystemRoles',
          meta: {
            title: '角色管理',
            icon: 'Avatar'
          }
        },
        {
          path: '/system/permissions',
          name: 'SystemPermissions',
          meta: {
            title: '权限管理',
            icon: 'Key'
          }
        },
        {
          path: '/system/config',
          name: 'SystemConfig',
          meta: {
            title: '系统配置',
            icon: 'Tools'
          }
        }
      ]
    }
  ]
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.sidebar-container {
  transition: width 0.28s;
  width: $sideBarWidth !important;
  background-color: $menuBg;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  
  // reset element-ui css
  .horizontal-collapse-transition {
    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
  }
  
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }
  
  .el-scrollbar__bar.is-vertical {
    right: 0px;
  }
  
  .el-scrollbar {
    height: 100%;
  }
  
  &.has-logo {
    .el-scrollbar {
      height: calc(100% - 50px);
    }
  }
  
  .is-horizontal {
    display: none;
  }
  
  a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
  }
  
  .svg-icon {
    margin-right: 16px;
  }
  
  .sub-el-icon {
    margin-right: 12px;
    margin-left: -2px;
  }
  
  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }
  
  // menu hover
  .submenu-title-noDropdown,
  .el-submenu__title {
    &:hover {
      background-color: $menuHover !important;
    }
  }
  
  .is-active > .el-submenu__title {
    color: $subMenuActiveText !important;
  }
  
  & .nest-menu .el-submenu > .el-submenu__title,
  & .el-submenu .el-menu-item {
    background-color: $subMenuBg !important;
    
    &:hover {
      background-color: $subMenuHover !important;
    }
  }
}

.hideSidebar {
  .sidebar-container {
    width: 54px !important;
  }
  
  .main-container {
    margin-left: 54px;
  }
  
  .submenu-title-noDropdown {
    padding: 0 !important;
    position: relative;
    
    .el-tooltip {
      padding: 0 !important;
      
      .svg-icon {
        margin-left: 20px;
      }
      
      .sub-el-icon {
        margin-left: 19px;
      }
    }
  }
  
  .el-submenu {
    overflow: hidden;
    
    & > .el-submenu__title {
      padding: 0 !important;
      
      .svg-icon {
        margin-left: 20px;
      }
      
      .sub-el-icon {
        margin-left: 19px;
      }
      
      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }
  
  .el-menu--collapse {
    .el-submenu {
      & > .el-submenu__title {
        & > span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

.el-menu--collapse .el-menu .el-submenu {
  min-width: $sideBarWidth !important;
}

// mobile responsive
.mobile {
  .main-container {
    margin-left: 0px;
  }
  
  .sidebar-container {
    transition: transform 0.28s;
    width: $sideBarWidth !important;
  }
  
  &.hideSidebar {
    .sidebar-container {
      pointer-events: none;
      transition-duration: 0.3s;
      transform: translate3d(-$sideBarWidth, 0, 0);
    }
  }
}

.withoutAnimation {
  .main-container,
  .sidebar-container {
    transition: none;
  }
}

// 侧边栏 Logo
.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  
  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    
    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }
    
    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }
  
  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}

.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}
</style>