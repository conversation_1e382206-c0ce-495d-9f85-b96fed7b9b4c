{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/image/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Image from './src/image.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElImage: SFCWithInstall<typeof Image> = withInstall(Image)\nexport default ElImage\n\nexport * from './src/image'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}